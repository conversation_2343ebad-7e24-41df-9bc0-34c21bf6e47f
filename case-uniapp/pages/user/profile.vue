<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-info-section" v-if="userInfo">
      <view class="user-header">
        <image class="user-avatar-large" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name-large">{{ userInfo.nickName }}</text>
          <text class="user-email" v-if="userInfo.email">{{ userInfo.email }}</text>
          <text class="user-phone" v-if="userInfo.phone">{{ userInfo.phone }}</text>
        </view>
      </view>
      
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userCases.length }}</text>
          <text class="stat-label">发布案例</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ totalViews }}</text>
          <text class="stat-label">总浏览量</text>
        </view>
      </view>
    </view>

    <!-- 案例列表 -->
    <view class="cases-section">
      <view class="section-header">
        <text class="section-title">发布的案例</text>
        <text class="case-count">共{{ userCases.length }}个</text>
      </view>

      <view class="case-list" v-if="userCases.length > 0">
        <view 
          class="case-item" 
          v-for="caseItem in userCases" 
          :key="caseItem.caseId"
          @click="goToCaseDetail(caseItem.caseId)"
        >
          <!-- 案例图片 -->
          <view class="case-image-container" v-if="caseItem.caseImages">
            <image 
              class="case-image" 
              :src="processImages(caseItem.caseImages)[0]" 
              mode="aspectFill"
            ></image>
          </view>
          
          <!-- 案例信息 -->
          <view class="case-info">
            <view class="case-title">{{ caseItem.caseTitle }}</view>
            <view class="case-content-preview">
              {{ processCaseContent(caseItem.caseContent, 80) }}
            </view>
            
            <!-- 案例标签 -->
            <view class="case-tags" v-if="caseItem.caseTags">
              <text 
                class="tag" 
                v-for="tag in processTags(caseItem.caseTags).slice(0, 3)" 
                :key="tag"
              >#{{ tag }}</text>
            </view>
            
            <!-- 案例统计 -->
            <view class="case-meta">
              <text class="publish-time">{{ formatTime(caseItem.createTime) }}</text>
              <text class="view-count">{{ caseItem.clickCount }} 次查看</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <image class="empty-image" src="/static/empty-case.png" mode="aspectFit"></image>
        <text class="empty-text">暂无发布的案例</text>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import { caseUserApi, caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processTags, processCaseContent } from '@/utils/utils.js'

export default {
  data() {
    return {
      userId: null,
      userInfo: null,
      userCases: [],
      loading: false
    }
  },
  
  computed: {
    // 计算总浏览量
    totalViews() {
      return this.userCases.reduce((total, caseItem) => total + (caseItem.clickCount || 0), 0)
    }
  },
  
  onLoad(options) {
    this.userId = options.userId
    if (this.userId) {
      this.loadUserData()
    }
  },
  
  onPullDownRefresh() {
    this.loadUserData().then(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    // 加载用户数据
    async loadUserData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadUserInfo(),
          this.loadUserCases()
        ])
      } catch (error) {
        console.error('加载用户数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 加载用户信息
    async loadUserInfo() {
      try {
        const res = await caseUserApi.getUserDetail(this.userId)
        this.userInfo = res.data
        
        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.userInfo.nickName + '的主页'
        })
      } catch (error) {
        console.error('加载用户信息失败:', error)
        uni.showToast({
          title: '用户不存在',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
    
    // 加载用户案例
    async loadUserCases() {
      try {
        const res = await caseInfoApi.getCasesByPublisher(this.userId)
        this.userCases = res.data || []
      } catch (error) {
        console.error('加载用户案例失败:', error)
        this.userCases = []
      }
    },
    
    // 跳转到案例详情
    goToCaseDetail(caseId) {
      uni.navigateTo({
        url: `/pages/case/detail?caseId=${caseId}`
      })
    },
    
    // 工具方法
    formatTime,
    processImages,
    processTags,
    processCaseContent
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-info-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar-large {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.user-name-large {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.user-email, .user-phone {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 案例列表区域 */
.cases-section {
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.case-count {
  font-size: 26rpx;
  color: #999;
}

/* 案例列表 */
.case-list {
  margin-bottom: 20rpx;
}

.case-item {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.case-image-container {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.case-image {
  width: 100%;
  height: 100%;
}

.case-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.case-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.case-content-preview {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.case-tags {
  margin-bottom: 10rpx;
}

.tag {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}

.case-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.publish-time, .view-count {
  font-size: 22rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
</style>
