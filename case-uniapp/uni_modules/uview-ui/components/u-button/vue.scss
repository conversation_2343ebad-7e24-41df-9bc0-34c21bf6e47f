// nvue下hover-class无效
$u-button-before-top:50% !default;
$u-button-before-left:50% !default;
$u-button-before-width:100% !default;
$u-button-before-height:100% !default;
$u-button-before-transform:translate(-50%, -50%) !default;
$u-button-before-opacity:0 !default;
$u-button-before-background-color:#000 !default;
$u-button-before-border-color:#000 !default;
$u-button-active-before-opacity:.15 !default;
$u-button-icon-margin-left:4px !default;
$u-button-plain-u-button-info-color:$u-info;
$u-button-plain-u-button-success-color:$u-success;
$u-button-plain-u-button-error-color:$u-error;
$u-button-plain-u-button-warning-color:$u-error;

.u-button {
	width: 100%;
	
	&__text {
		white-space: nowrap;
		line-height: 1;
	}
	
	&:before {
		position: absolute;
		top:$u-button-before-top;
		left:$u-button-before-left;
		width:$u-button-before-width;
		height:$u-button-before-height;
		border: inherit;
		border-radius: inherit;
		transform:$u-button-before-transform;
		opacity:$u-button-before-opacity;
		content: " ";
		background-color:$u-button-before-background-color;
		border-color:$u-button-before-border-color;
	}
	
	&--active {
		&:before {
			opacity: .15
		}
	}
	
	&__icon+&__text:not(:empty),
	&__loading-text {
		margin-left:$u-button-icon-margin-left;
	}
	
	&--plain {
		&.u-button--primary {
			color: $u-primary;
		}
	}
	
	&--plain {
		&.u-button--info {
			color:$u-button-plain-u-button-info-color;
		}
	}
	
	&--plain {
		&.u-button--success {
			color:$u-button-plain-u-button-success-color;
		}
	}
	
	&--plain {
		&.u-button--error {
			color:$u-button-plain-u-button-error-color;
		}
	}
	
	&--plain {
		&.u-button--warning {
			color:$u-button-plain-u-button-warning-color;
		}
	}
}
