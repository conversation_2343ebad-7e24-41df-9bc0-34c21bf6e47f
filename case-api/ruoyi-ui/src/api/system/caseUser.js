import request from '@/utils/request'

// 查询案例用户列表
export function listCaseUser(query) {
  return request({
    url: '/system/caseUser/list',
    method: 'get',
    params: query
  })
}

// 查询案例用户详细
export function getCaseUser(userId) {
  return request({
    url: '/system/caseUser/' + userId,
    method: 'get'
  })
}

// 新增案例用户
export function addCaseUser(data) {
  return request({
    url: '/system/caseUser',
    method: 'post',
    data: data
  })
}

// 修改案例用户
export function updateCaseUser(data) {
  return request({
    url: '/system/caseUser',
    method: 'put',
    data: data
  })
}

// 删除案例用户
export function delCaseUser(userId) {
  return request({
    url: '/system/caseUser/' + userId,
    method: 'delete'
  })
}

// 导出案例用户
export function exportCaseUser(query) {
  return request({
    url: '/system/caseUser/export',
    method: 'post',
    params: query
  })
}

// 获取优秀客户列表（移动端）
export function getExcellentUsers() {
  return request({
    url: '/system/caseUser/mobile/excellent',
    method: 'get'
  })
}

// 获取案例用户详细信息（移动端）
export function getMobileCaseUser(userId) {
  return request({
    url: '/system/caseUser/mobile/' + userId,
    method: 'get'
  })
}
