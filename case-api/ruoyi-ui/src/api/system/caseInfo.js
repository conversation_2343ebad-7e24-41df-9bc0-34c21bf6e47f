import request from '@/utils/request'

// 查询案例信息列表
export function listCaseInfo(query) {
  return request({
    url: '/system/caseInfo/list',
    method: 'get',
    params: query
  })
}

// 查询案例信息详细
export function getCaseInfo(caseId) {
  return request({
    url: '/system/caseInfo/' + caseId,
    method: 'get'
  })
}

// 新增案例信息
export function addCaseInfo(data) {
  return request({
    url: '/system/caseInfo',
    method: 'post',
    data: data
  })
}

// 修改案例信息
export function updateCaseInfo(data) {
  return request({
    url: '/system/caseInfo',
    method: 'put',
    data: data
  })
}

// 删除案例信息
export function delCaseInfo(caseId) {
  return request({
    url: '/system/caseInfo/' + caseId,
    method: 'delete'
  })
}

// 导出案例信息
export function exportCaseInfo(query) {
  return request({
    url: '/system/caseInfo/export',
    method: 'post',
    params: query
  })
}

// 获取推荐案例列表（移动端）
export function getRecommendedCases(limit) {
  return request({
    url: '/system/caseInfo/mobile/recommended',
    method: 'get',
    params: { limit }
  })
}

// 获取最新案例列表（移动端）
export function getLatestCases(limit) {
  return request({
    url: '/system/caseInfo/mobile/latest',
    method: 'get',
    params: { limit }
  })
}

// 根据发布人ID获取案例列表（移动端）
export function getCasesByPublisher(publisherId) {
  return request({
    url: '/system/caseInfo/mobile/publisher/' + publisherId,
    method: 'get'
  })
}

// 根据标签获取案例列表（移动端）
export function getCasesByTag(tag) {
  return request({
    url: '/system/caseInfo/mobile/tag/' + tag,
    method: 'get'
  })
}

// 获取案例详细信息（移动端）
export function getMobileCaseInfo(caseId) {
  return request({
    url: '/system/caseInfo/mobile/' + caseId,
    method: 'get'
  })
}

// 增加案例点击次数（移动端）
export function incrementClick(caseId) {
  return request({
    url: '/system/caseInfo/mobile/click/' + caseId,
    method: 'post'
  })
}
