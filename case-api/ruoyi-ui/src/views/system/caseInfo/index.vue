<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="案例标题" prop="caseTitle">
        <el-input
          v-model="queryParams.caseTitle"
          placeholder="请输入案例标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模板类型" prop="templateType">
        <el-input
          v-model="queryParams.templateType"
          placeholder="请输入模板类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="案例标签" prop="caseTags">
        <el-input
          v-model="queryParams.caseTags"
          placeholder="请输入案例标签"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否推荐" prop="isRecommended">
        <el-select v-model="queryParams.isRecommended" placeholder="请选择是否推荐" clearable>
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:caseInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:caseInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:caseInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:caseInfo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="caseInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="案例ID" align="center" prop="caseId" />
      <el-table-column label="案例标题" align="center" prop="caseTitle" :show-overflow-tooltip="true" />
      <el-table-column label="案例图片" align="center" prop="caseImages" width="100">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.caseImages" :src="scope.row.caseImages.split(',')[0]" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="模板类型" align="center" prop="templateType" />
      <el-table-column label="案例标签" align="center" prop="caseTags" :show-overflow-tooltip="true" />
      <el-table-column label="点击次数" align="center" prop="clickCount" />
      <el-table-column label="发布人" align="center" prop="publisher.nickName" />
      <el-table-column label="是否推荐" align="center" prop="isRecommended">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isRecommended"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:caseInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:caseInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改案例信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例标题" prop="caseTitle">
              <el-input v-model="form.caseTitle" placeholder="请输入案例标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板类型" prop="templateType">
              <el-input v-model="form.templateType" placeholder="请输入模板类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例标签" prop="caseTags">
              <el-input v-model="form.caseTags" placeholder="请输入案例标签，多个用逗号分隔" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布人" prop="publisherId">
              <el-select v-model="form.publisherId" placeholder="请选择发布人" filterable>
                <el-option
                  v-for="user in caseUserOptions"
                  :key="user.userId"
                  :label="user.nickName"
                  :value="user.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例内容" prop="caseContent">
          <editor v-model="form.caseContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="案例图片" prop="caseImages">
          <image-upload v-model="form.caseImages"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否推荐" prop="isRecommended">
              <el-radio-group v-model="form.isRecommended">
                <el-radio
                  v-for="dict in dict.type.sys_yes_no"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCaseInfo, getCaseInfo, delCaseInfo, addCaseInfo, updateCaseInfo } from "@/api/system/caseInfo";
import { listCaseUser } from "@/api/system/caseUser";

export default {
  name: "CaseInfo",
  dicts: ['sys_yes_no', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 案例信息表格数据
      caseInfoList: [],
      // 案例用户选项
      caseUserOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        caseTitle: null,
        templateType: null,
        caseTags: null,
        publisherId: null,
        isRecommended: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        caseTitle: [
          { required: true, message: "案例标题不能为空", trigger: "blur" }
        ],
        publisherId: [
          { required: true, message: "发布人不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCaseUserOptions();
  },
  methods: {
    /** 查询案例信息列表 */
    getList() {
      this.loading = true;
      listCaseInfo(this.queryParams).then(response => {
        this.caseInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取案例用户选项 */
    getCaseUserOptions() {
      listCaseUser({ status: '0' }).then(response => {
        this.caseUserOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        caseId: null,
        caseTitle: null,
        caseContent: null,
        caseImages: null,
        templateType: null,
        caseTags: null,
        clickCount: 0,
        publisherId: null,
        isRecommended: "0",
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.caseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加案例信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const caseId = row.caseId || this.ids
      getCaseInfo(caseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改案例信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.caseId != null) {
            updateCaseInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCaseInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const caseIds = row.caseId || this.ids;
      this.$modal.confirm('是否确认删除案例信息编号为"' + caseIds + '"的数据项？').then(function() {
        return delCaseInfo(caseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/caseInfo/export', {
        ...this.queryParams
      }, `caseInfo_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
