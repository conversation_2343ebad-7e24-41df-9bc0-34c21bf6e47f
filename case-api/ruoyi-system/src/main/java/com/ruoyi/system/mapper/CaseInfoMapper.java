package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.CaseInfo;

/**
 * 案例信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface CaseInfoMapper 
{
    /**
     * 查询案例信息
     * 
     * @param caseId 案例信息主键
     * @return 案例信息
     */
    public CaseInfo selectCaseInfoByCaseId(Long caseId);

    /**
     * 查询案例信息列表
     * 
     * @param caseInfo 案例信息
     * @return 案例信息集合
     */
    public List<CaseInfo> selectCaseInfoList(CaseInfo caseInfo);

    /**
     * 查询推荐案例列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例信息集合
     */
    public List<CaseInfo> selectRecommendedCases(@Param("limit") Integer limit);

    /**
     * 查询最新案例列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例信息集合
     */
    public List<CaseInfo> selectLatestCases(@Param("limit") Integer limit);

    /**
     * 根据发布人ID查询案例列表
     * 
     * @param publisherId 发布人ID
     * @return 案例信息集合
     */
    public List<CaseInfo> selectCaseInfoByPublisherId(Long publisherId);

    /**
     * 根据标签查询案例列表
     * 
     * @param tag 标签
     * @return 案例信息集合
     */
    public List<CaseInfo> selectCaseInfoByTag(String tag);

    /**
     * 新增案例信息
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    public int insertCaseInfo(CaseInfo caseInfo);

    /**
     * 修改案例信息
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    public int updateCaseInfo(CaseInfo caseInfo);

    /**
     * 增加点击次数
     * 
     * @param caseId 案例ID
     * @return 结果
     */
    public int incrementClickCount(Long caseId);

    /**
     * 删除案例信息
     * 
     * @param caseId 案例信息主键
     * @return 结果
     */
    public int deleteCaseInfoByCaseId(Long caseId);

    /**
     * 批量删除案例信息
     * 
     * @param caseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCaseInfoByCaseIds(Long[] caseIds);

    /**
     * 校验案例标题是否唯一
     * 
     * @param caseTitle 案例标题
     * @return 结果
     */
    public CaseInfo checkCaseTitleUnique(String caseTitle);
}
