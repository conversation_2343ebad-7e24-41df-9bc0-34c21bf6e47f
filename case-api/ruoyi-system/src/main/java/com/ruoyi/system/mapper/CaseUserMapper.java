package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.CaseUser;

/**
 * 案例用户Mapper接口
 * 
 * <AUTHOR>
 */
public interface CaseUserMapper 
{
    /**
     * 查询案例用户
     * 
     * @param userId 案例用户主键
     * @return 案例用户
     */
    public CaseUser selectCaseUserByUserId(Long userId);

    /**
     * 查询案例用户列表
     * 
     * @param caseUser 案例用户
     * @return 案例用户集合
     */
    public List<CaseUser> selectCaseUserList(CaseUser caseUser);

    /**
     * 查询优秀客户列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例用户集合
     */
    public List<CaseUser> selectExcellentUsers(@Param("limit") Integer limit);

    /**
     * 新增案例用户
     * 
     * @param caseUser 案例用户
     * @return 结果
     */
    public int insertCaseUser(CaseUser caseUser);

    /**
     * 修改案例用户
     * 
     * @param caseUser 案例用户
     * @return 结果
     */
    public int updateCaseUser(CaseUser caseUser);

    /**
     * 修改用户头像
     * 
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    public int updateCaseUserAvatar(@Param("userId") Long userId, @Param("avatar") String avatar);

    /**
     * 删除案例用户
     * 
     * @param userId 案例用户主键
     * @return 结果
     */
    public int deleteCaseUserByUserId(Long userId);

    /**
     * 批量删除案例用户
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCaseUserByUserIds(Long[] userIds);

    /**
     * 校验昵称是否唯一
     * 
     * @param nickName 昵称
     * @return 结果
     */
    public CaseUser checkNickNameUnique(String nickName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phone 手机号码
     * @return 结果
     */
    public CaseUser checkPhoneUnique(String phone);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public CaseUser checkEmailUnique(String email);
}
