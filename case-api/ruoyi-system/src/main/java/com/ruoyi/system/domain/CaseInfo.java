package com.ruoyi.system.domain;

import javax.validation.constraints.*;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.annotation.Excel.Type;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.xss.Xss;

/**
 * 案例信息对象 case_info
 * 
 * <AUTHOR>
 */
public class CaseInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 案例ID */
    @Excel(name = "案例序号", type = Type.EXPORT, cellType = ColumnType.NUMERIC, prompt = "案例编号")
    private Long caseId;

    /** 案例标题 */
    @Excel(name = "案例标题")
    private String caseTitle;

    /** 案例内容（富文本） */
    @Excel(name = "案例内容")
    private String caseContent;

    /** 案例图片（多张，逗号分隔） */
    @Excel(name = "案例图片")
    private String caseImages;

    /** 模板类型 */
    @Excel(name = "模板类型")
    private String templateType;

    /** 案例标签（多个标签逗号分隔） */
    @Excel(name = "案例标签")
    private String caseTags;

    /** 点击次数 */
    @Excel(name = "点击次数", cellType = ColumnType.NUMERIC)
    private Integer clickCount;

    /** 发布人ID（关联案例用户） */
    @Excel(name = "发布人ID", cellType = ColumnType.NUMERIC)
    private Long publisherId;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private String isRecommended;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 发布人信息 */
    private CaseUser publisher;

    public CaseInfo()
    {

    }

    public CaseInfo(Long caseId)
    {
        this.caseId = caseId;
    }

    public Long getCaseId()
    {
        return caseId;
    }

    public void setCaseId(Long caseId)
    {
        this.caseId = caseId;
    }

    @Xss(message = "案例标题不能包含脚本字符")
    @NotBlank(message = "案例标题不能为空")
    @Size(min = 0, max = 100, message = "案例标题长度不能超过100个字符")
    public String getCaseTitle()
    {
        return caseTitle;
    }

    public void setCaseTitle(String caseTitle)
    {
        this.caseTitle = caseTitle;
    }

    public String getCaseContent()
    {
        return caseContent;
    }

    public void setCaseContent(String caseContent)
    {
        this.caseContent = caseContent;
    }

    public String getCaseImages()
    {
        return caseImages;
    }

    public void setCaseImages(String caseImages)
    {
        this.caseImages = caseImages;
    }

    @Size(min = 0, max = 20, message = "模板类型长度不能超过20个字符")
    public String getTemplateType()
    {
        return templateType;
    }

    public void setTemplateType(String templateType)
    {
        this.templateType = templateType;
    }

    @Size(min = 0, max = 200, message = "案例标签长度不能超过200个字符")
    public String getCaseTags()
    {
        return caseTags;
    }

    public void setCaseTags(String caseTags)
    {
        this.caseTags = caseTags;
    }

    public Integer getClickCount()
    {
        return clickCount;
    }

    public void setClickCount(Integer clickCount)
    {
        this.clickCount = clickCount;
    }

    @NotNull(message = "发布人ID不能为空")
    public Long getPublisherId()
    {
        return publisherId;
    }

    public void setPublisherId(Long publisherId)
    {
        this.publisherId = publisherId;
    }

    public String getIsRecommended()
    {
        return isRecommended;
    }

    public void setIsRecommended(String isRecommended)
    {
        this.isRecommended = isRecommended;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public CaseUser getPublisher()
    {
        return publisher;
    }

    public void setPublisher(CaseUser publisher)
    {
        this.publisher = publisher;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("caseId", getCaseId())
            .append("caseTitle", getCaseTitle())
            .append("caseContent", getCaseContent())
            .append("caseImages", getCaseImages())
            .append("templateType", getTemplateType())
            .append("caseTags", getCaseTags())
            .append("clickCount", getClickCount())
            .append("publisherId", getPublisherId())
            .append("isRecommended", getIsRecommended())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
