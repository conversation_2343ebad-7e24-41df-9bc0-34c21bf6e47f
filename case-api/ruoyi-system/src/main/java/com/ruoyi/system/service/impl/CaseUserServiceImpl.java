package com.ruoyi.system.service.impl;

import java.util.List;
import javax.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.CaseUser;
import com.ruoyi.system.mapper.CaseUserMapper;
import com.ruoyi.system.service.ICaseUserService;

/**
 * 案例用户Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class CaseUserServiceImpl implements ICaseUserService 
{
    private static final Logger log = LoggerFactory.getLogger(CaseUserServiceImpl.class);

    @Autowired
    private CaseUserMapper caseUserMapper;

    @Autowired
    protected Validator validator;

    /**
     * 查询案例用户
     * 
     * @param userId 案例用户主键
     * @return 案例用户
     */
    @Override
    public CaseUser selectCaseUserByUserId(Long userId)
    {
        return caseUserMapper.selectCaseUserByUserId(userId);
    }

    /**
     * 查询案例用户列表
     * 
     * @param caseUser 案例用户
     * @return 案例用户
     */
    @Override
    public List<CaseUser> selectCaseUserList(CaseUser caseUser)
    {
        return caseUserMapper.selectCaseUserList(caseUser);
    }

    /**
     * 查询优秀客户列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例用户集合
     */
    @Override
    public List<CaseUser> selectExcellentUsers(Integer limit)
    {
        return caseUserMapper.selectExcellentUsers(limit);
    }

    /**
     * 新增案例用户
     * 
     * @param caseUser 案例用户
     * @return 结果
     */
    @Override
    public int insertCaseUser(CaseUser caseUser)
    {
        return caseUserMapper.insertCaseUser(caseUser);
    }

    /**
     * 修改案例用户
     * 
     * @param caseUser 案例用户
     * @return 结果
     */
    @Override
    public int updateCaseUser(CaseUser caseUser)
    {
        return caseUserMapper.updateCaseUser(caseUser);
    }

    /**
     * 修改用户头像
     * 
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateCaseUserAvatar(Long userId, String avatar)
    {
        return caseUserMapper.updateCaseUserAvatar(userId, avatar) > 0;
    }

    /**
     * 批量删除案例用户
     * 
     * @param userIds 需要删除的案例用户主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCaseUserByUserIds(Long[] userIds)
    {
        return caseUserMapper.deleteCaseUserByUserIds(userIds);
    }

    /**
     * 删除案例用户信息
     * 
     * @param userId 案例用户主键
     * @return 结果
     */
    @Override
    public int deleteCaseUserByUserId(Long userId)
    {
        return caseUserMapper.deleteCaseUserByUserId(userId);
    }

    /**
     * 校验昵称是否唯一
     * 
     * @param caseUser 案例用户信息
     * @return 结果
     */
    @Override
    public boolean checkNickNameUnique(CaseUser caseUser)
    {
        Long userId = StringUtils.isNull(caseUser.getUserId()) ? -1L : caseUser.getUserId();
        CaseUser info = caseUserMapper.checkNickNameUnique(caseUser.getNickName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param caseUser 案例用户信息
     * @return 结果
     */
    @Override
    public boolean checkPhoneUnique(CaseUser caseUser)
    {
        Long userId = StringUtils.isNull(caseUser.getUserId()) ? -1L : caseUser.getUserId();
        CaseUser info = caseUserMapper.checkPhoneUnique(caseUser.getPhone());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验email是否唯一
     *
     * @param caseUser 案例用户信息
     * @return 结果
     */
    @Override
    public boolean checkEmailUnique(CaseUser caseUser)
    {
        Long userId = StringUtils.isNull(caseUser.getUserId()) ? -1L : caseUser.getUserId();
        CaseUser info = caseUserMapper.checkEmailUnique(caseUser.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 导入案例用户数据
     * 
     * @param userList 案例用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importCaseUser(List<CaseUser> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入案例用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CaseUser caseUser : userList)
        {
            try
            {
                // 验证是否存在这个用户
                CaseUser u = caseUserMapper.checkNickNameUnique(caseUser.getNickName());
                if (StringUtils.isNull(u))
                {
                    BeanValidators.validateWithException(validator, caseUser);
                    caseUser.setCreateBy(operName);
                    caseUserMapper.insertCaseUser(caseUser);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、昵称 " + caseUser.getNickName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, caseUser);
                    caseUser.setUserId(u.getUserId());
                    caseUser.setUpdateBy(operName);
                    caseUserMapper.updateCaseUser(caseUser);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、昵称 " + caseUser.getNickName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、昵称 " + caseUser.getNickName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、昵称 " + caseUser.getNickName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
