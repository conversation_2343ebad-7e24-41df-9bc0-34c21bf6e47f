package com.ruoyi.system.service.impl;

import java.util.List;
import javax.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.CaseInfo;
import com.ruoyi.system.mapper.CaseInfoMapper;
import com.ruoyi.system.service.ICaseInfoService;

/**
 * 案例信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class CaseInfoServiceImpl implements ICaseInfoService 
{
    private static final Logger log = LoggerFactory.getLogger(CaseInfoServiceImpl.class);

    @Autowired
    private CaseInfoMapper caseInfoMapper;

    @Autowired
    protected Validator validator;

    /**
     * 查询案例信息
     * 
     * @param caseId 案例信息主键
     * @return 案例信息
     */
    @Override
    public CaseInfo selectCaseInfoByCaseId(Long caseId)
    {
        return caseInfoMapper.selectCaseInfoByCaseId(caseId);
    }

    /**
     * 查询案例信息列表
     * 
     * @param caseInfo 案例信息
     * @return 案例信息
     */
    @Override
    public List<CaseInfo> selectCaseInfoList(CaseInfo caseInfo)
    {
        return caseInfoMapper.selectCaseInfoList(caseInfo);
    }

    /**
     * 查询推荐案例列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例信息集合
     */
    @Override
    public List<CaseInfo> selectRecommendedCases(Integer limit)
    {
        return caseInfoMapper.selectRecommendedCases(limit);
    }

    /**
     * 查询最新案例列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例信息集合
     */
    @Override
    public List<CaseInfo> selectLatestCases(Integer limit)
    {
        return caseInfoMapper.selectLatestCases(limit);
    }

    /**
     * 根据发布人ID查询案例列表
     * 
     * @param publisherId 发布人ID
     * @return 案例信息集合
     */
    @Override
    public List<CaseInfo> selectCaseInfoByPublisherId(Long publisherId)
    {
        return caseInfoMapper.selectCaseInfoByPublisherId(publisherId);
    }

    /**
     * 根据标签查询案例列表
     * 
     * @param tag 标签
     * @return 案例信息集合
     */
    @Override
    public List<CaseInfo> selectCaseInfoByTag(String tag)
    {
        return caseInfoMapper.selectCaseInfoByTag(tag);
    }

    /**
     * 新增案例信息
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    @Override
    public int insertCaseInfo(CaseInfo caseInfo)
    {
        return caseInfoMapper.insertCaseInfo(caseInfo);
    }

    /**
     * 修改案例信息
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    @Override
    public int updateCaseInfo(CaseInfo caseInfo)
    {
        return caseInfoMapper.updateCaseInfo(caseInfo);
    }

    /**
     * 增加点击次数
     * 
     * @param caseId 案例ID
     * @return 结果
     */
    @Override
    public int incrementClickCount(Long caseId)
    {
        return caseInfoMapper.incrementClickCount(caseId);
    }

    /**
     * 批量删除案例信息
     * 
     * @param caseIds 需要删除的案例信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCaseInfoByCaseIds(Long[] caseIds)
    {
        return caseInfoMapper.deleteCaseInfoByCaseIds(caseIds);
    }

    /**
     * 删除案例信息信息
     * 
     * @param caseId 案例信息主键
     * @return 结果
     */
    @Override
    public int deleteCaseInfoByCaseId(Long caseId)
    {
        return caseInfoMapper.deleteCaseInfoByCaseId(caseId);
    }

    /**
     * 校验案例标题是否唯一
     * 
     * @param caseInfo 案例信息
     * @return 结果
     */
    @Override
    public boolean checkCaseTitleUnique(CaseInfo caseInfo)
    {
        Long caseId = StringUtils.isNull(caseInfo.getCaseId()) ? -1L : caseInfo.getCaseId();
        CaseInfo info = caseInfoMapper.checkCaseTitleUnique(caseInfo.getCaseTitle());
        if (StringUtils.isNotNull(info) && info.getCaseId().longValue() != caseId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 导入案例信息数据
     * 
     * @param caseInfoList 案例信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importCaseInfo(List<CaseInfo> caseInfoList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(caseInfoList) || caseInfoList.size() == 0)
        {
            throw new ServiceException("导入案例信息数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CaseInfo caseInfo : caseInfoList)
        {
            try
            {
                // 验证是否存在这个案例
                CaseInfo c = caseInfoMapper.checkCaseTitleUnique(caseInfo.getCaseTitle());
                if (StringUtils.isNull(c))
                {
                    BeanValidators.validateWithException(validator, caseInfo);
                    caseInfo.setCreateBy(operName);
                    caseInfoMapper.insertCaseInfo(caseInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、案例标题 " + caseInfo.getCaseTitle() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, caseInfo);
                    caseInfo.setCaseId(c.getCaseId());
                    caseInfo.setUpdateBy(operName);
                    caseInfoMapper.updateCaseInfo(caseInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、案例标题 " + caseInfo.getCaseTitle() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、案例标题 " + caseInfo.getCaseTitle() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、案例标题 " + caseInfo.getCaseTitle() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
