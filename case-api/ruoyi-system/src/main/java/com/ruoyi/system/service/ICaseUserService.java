package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CaseUser;

/**
 * 案例用户Service接口
 * 
 * <AUTHOR>
 */
public interface ICaseUserService 
{
    /**
     * 查询案例用户
     * 
     * @param userId 案例用户主键
     * @return 案例用户
     */
    public CaseUser selectCaseUserByUserId(Long userId);

    /**
     * 查询案例用户列表
     * 
     * @param caseUser 案例用户
     * @return 案例用户集合
     */
    public List<CaseUser> selectCaseUserList(CaseUser caseUser);

    /**
     * 查询优秀客户列表（用于首页展示）
     * 
     * @param limit 限制数量
     * @return 案例用户集合
     */
    public List<CaseUser> selectExcellentUsers(Integer limit);

    /**
     * 新增案例用户
     * 
     * @param caseUser 案例用户
     * @return 结果
     */
    public int insertCaseUser(CaseUser caseUser);

    /**
     * 修改案例用户
     * 
     * @param caseUser 案例用户
     * @return 结果
     */
    public int updateCaseUser(CaseUser caseUser);

    /**
     * 修改用户头像
     * 
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateCaseUserAvatar(Long userId, String avatar);

    /**
     * 批量删除案例用户
     * 
     * @param userIds 需要删除的案例用户主键集合
     * @return 结果
     */
    public int deleteCaseUserByUserIds(Long[] userIds);

    /**
     * 删除案例用户信息
     * 
     * @param userId 案例用户主键
     * @return 结果
     */
    public int deleteCaseUserByUserId(Long userId);

    /**
     * 校验昵称是否唯一
     * 
     * @param caseUser 案例用户信息
     * @return 结果
     */
    public boolean checkNickNameUnique(CaseUser caseUser);

    /**
     * 校验手机号码是否唯一
     *
     * @param caseUser 案例用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(CaseUser caseUser);

    /**
     * 校验email是否唯一
     *
     * @param caseUser 案例用户信息
     * @return 结果
     */
    public boolean checkEmailUnique(CaseUser caseUser);

    /**
     * 导入案例用户数据
     * 
     * @param userList 案例用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importCaseUser(List<CaseUser> userList, Boolean isUpdateSupport, String operName);
}
