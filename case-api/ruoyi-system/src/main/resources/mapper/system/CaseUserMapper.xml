<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CaseUserMapper">
    
    <resultMap type="CaseUser" id="CaseUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="nickName"     column="nick_name"    />
        <result property="avatar"       column="avatar"       />
        <result property="phone"        column="phone"        />
        <result property="email"        column="email"        />
        <result property="sex"          column="sex"          />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectCaseUserVo">
        select user_id, nick_name, avatar, phone, email, sex, status, del_flag, create_by, create_time, update_by, update_time, remark from case_user
    </sql>

    <select id="selectCaseUserList" parameterType="CaseUser" resultMap="CaseUserResult">
        <include refid="selectCaseUserVo"/>
        where del_flag = '0'
        <if test="userId != null and userId != 0">
            AND user_id = #{userId}
        </if>
        <if test="nickName != null and nickName != ''">
            AND nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="phone != null and phone != ''">
            AND phone like concat('%', #{phone}, '%')
        </if>
        <if test="email != null and email != ''">
            AND email like concat('%', #{email}, '%')
        </if>
        <if test="sex != null and sex != ''">
            AND sex = #{sex}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
        </if>
        order by create_time desc
    </select>
    
    <select id="selectExcellentUsers" resultMap="CaseUserResult">
        <include refid="selectCaseUserVo"/>
        where del_flag = '0' and status = '0'
        order by create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <select id="selectCaseUserByUserId" parameterType="Long" resultMap="CaseUserResult">
        <include refid="selectCaseUserVo"/>
        where user_id = #{userId}
    </select>
    
    <select id="checkNickNameUnique" parameterType="String" resultMap="CaseUserResult">
        select user_id, nick_name from case_user where nick_name = #{nickName} and del_flag = '0' limit 1
    </select>
    
    <select id="checkPhoneUnique" parameterType="String" resultMap="CaseUserResult">
        select user_id, phone from case_user where phone = #{phone} and del_flag = '0' limit 1
    </select>
    
    <select id="checkEmailUnique" parameterType="String" resultMap="CaseUserResult">
        select user_id, email from case_user where email = #{email} and del_flag = '0' limit 1
    </select>
    
    <insert id="insertCaseUser" parameterType="CaseUser" useGeneratedKeys="true" keyProperty="userId">
        insert into case_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="sex != null and sex != ''">sex,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="sex != null and sex != ''">#{sex},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateCaseUser" parameterType="CaseUser">
        update case_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where user_id = #{userId}
    </update>

    <update id="updateCaseUserAvatar" parameterType="String">
        update case_user set avatar = #{avatar} where user_id = #{userId}
    </update>

    <delete id="deleteCaseUserByUserId" parameterType="Long">
        update case_user set del_flag = '2' where user_id = #{userId}
    </delete>

    <delete id="deleteCaseUserByUserIds" parameterType="String">
        update case_user set del_flag = '2' where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>
