# API接口测试文档

## 测试环境
- 后端服务地址：http://localhost:8080
- 数据库：MySQL 5.7+
- 测试工具：Postman 或 curl

## 1. 案例用户管理接口测试

### 1.1 查询案例用户列表
```bash
GET http://localhost:8080/system/caseUser/list
```

### 1.2 获取案例用户详情
```bash
GET http://localhost:8080/system/caseUser/1
```

### 1.3 新增案例用户
```bash
POST http://localhost:8080/system/caseUser
Content-Type: application/json

{
  "nickName": "测试用户",
  "avatar": "/profile/avatar/test.jpg",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "sex": "0",
  "status": "0",
  "remark": "测试用户"
}
```

### 1.4 修改案例用户
```bash
PUT http://localhost:8080/system/caseUser
Content-Type: application/json

{
  "userId": 1,
  "nickName": "修改后的用户名",
  "avatar": "/profile/avatar/test.jpg",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "sex": "1",
  "status": "0",
  "remark": "修改后的备注"
}
```

### 1.5 删除案例用户
```bash
DELETE http://localhost:8080/system/caseUser/1
```

### 1.6 获取优秀客户列表（移动端）
```bash
GET http://localhost:8080/system/caseUser/mobile/excellent
```

## 2. 案例信息管理接口测试

### 2.1 查询案例信息列表
```bash
GET http://localhost:8080/system/caseInfo/list
```

### 2.2 获取案例信息详情
```bash
GET http://localhost:8080/system/caseInfo/1
```

### 2.3 新增案例信息
```bash
POST http://localhost:8080/system/caseInfo
Content-Type: application/json

{
  "caseTitle": "测试案例标题",
  "caseContent": "<p>这是一个测试案例的内容</p>",
  "caseImages": "/profile/upload/case1.jpg,/profile/upload/case2.jpg",
  "templateType": "测试模板",
  "caseTags": "测试,案例,标签",
  "publisherId": 1,
  "isRecommended": "1",
  "status": "0",
  "remark": "测试案例"
}
```

### 2.4 修改案例信息
```bash
PUT http://localhost:8080/system/caseInfo
Content-Type: application/json

{
  "caseId": 1,
  "caseTitle": "修改后的案例标题",
  "caseContent": "<p>修改后的案例内容</p>",
  "caseImages": "/profile/upload/case1.jpg",
  "templateType": "修改后的模板",
  "caseTags": "修改,案例",
  "publisherId": 1,
  "isRecommended": "0",
  "status": "0",
  "remark": "修改后的备注"
}
```

### 2.5 删除案例信息
```bash
DELETE http://localhost:8080/system/caseInfo/1
```

### 2.6 获取推荐案例列表（移动端）
```bash
GET http://localhost:8080/system/caseInfo/mobile/recommended?limit=10
```

### 2.7 获取最新案例列表（移动端）
```bash
GET http://localhost:8080/system/caseInfo/mobile/latest?limit=10
```

### 2.8 根据发布人ID获取案例列表（移动端）
```bash
GET http://localhost:8080/system/caseInfo/mobile/publisher/1
```

### 2.9 根据标签获取案例列表（移动端）
```bash
GET http://localhost:8080/system/caseInfo/mobile/tag/数字化
```

### 2.10 获取案例详情（移动端）
```bash
GET http://localhost:8080/system/caseInfo/mobile/1
```

### 2.11 增加案例点击次数（移动端）
```bash
POST http://localhost:8080/system/caseInfo/mobile/click/1
```

## 3. 测试步骤

### 3.1 环境准备
1. 启动后端服务
2. 确保数据库连接正常
3. 执行数据库初始化脚本

### 3.2 基础功能测试
1. 测试案例用户的CRUD操作
2. 测试案例信息的CRUD操作
3. 验证数据库中数据的正确性

### 3.3 移动端接口测试
1. 测试优秀客户列表接口
2. 测试推荐案例和最新案例接口
3. 测试案例详情和点击统计接口
4. 测试用户相关案例查询接口

### 3.4 数据验证
1. 验证返回数据格式是否正确
2. 验证分页功能是否正常
3. 验证搜索和过滤功能
4. 验证关联查询数据完整性

## 4. 预期结果

### 4.1 成功响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {...}
}
```

### 4.2 分页响应格式
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [...],
  "total": 100
}
```

### 4.3 错误响应格式
```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

## 5. 常见问题排查

### 5.1 数据库连接问题
- 检查数据库配置
- 确认数据库服务是否启动
- 验证用户权限

### 5.2 接口访问问题
- 检查服务是否启动
- 确认端口是否正确
- 验证请求格式

### 5.3 数据问题
- 检查必填字段
- 验证数据格式
- 确认外键关联

## 6. 测试完成标准

- [ ] 所有CRUD接口正常工作
- [ ] 移动端专用接口返回正确数据
- [ ] 数据库操作无异常
- [ ] 响应格式符合规范
- [ ] 错误处理机制正常
- [ ] 性能满足要求
