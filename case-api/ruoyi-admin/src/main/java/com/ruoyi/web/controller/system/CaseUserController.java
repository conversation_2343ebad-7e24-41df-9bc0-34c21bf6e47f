package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.CaseUser;
import com.ruoyi.system.service.ICaseUserService;

/**
 * 案例用户Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/caseUser")
public class CaseUserController extends BaseController
{
    @Autowired
    private ICaseUserService caseUserService;

    /**
     * 查询案例用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaseUser caseUser)
    {
        startPage();
        List<CaseUser> list = caseUserService.selectCaseUserList(caseUser);
        return getDataTable(list);
    }

    /**
     * 导出案例用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:export')")
    @Log(title = "案例用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaseUser caseUser)
    {
        List<CaseUser> list = caseUserService.selectCaseUserList(caseUser);
        ExcelUtil<CaseUser> util = new ExcelUtil<CaseUser>(CaseUser.class);
        util.exportExcel(response, list, "案例用户数据");
    }

    /**
     * 获取案例用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(caseUserService.selectCaseUserByUserId(userId));
    }

    /**
     * 新增案例用户
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:add')")
    @Log(title = "案例用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CaseUser caseUser)
    {
        if (!caseUserService.checkNickNameUnique(caseUser))
        {
            return error("新增案例用户'" + caseUser.getNickName() + "'失败，昵称已存在");
        }
        else if (StringUtils.isNotEmpty(caseUser.getPhone()) && !caseUserService.checkPhoneUnique(caseUser))
        {
            return error("新增案例用户'" + caseUser.getNickName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(caseUser.getEmail()) && !caseUserService.checkEmailUnique(caseUser))
        {
            return error("新增案例用户'" + caseUser.getNickName() + "'失败，邮箱账号已存在");
        }
        caseUser.setCreateBy(getUsername());
        return toAjax(caseUserService.insertCaseUser(caseUser));
    }

    /**
     * 修改案例用户
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:edit')")
    @Log(title = "案例用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CaseUser caseUser)
    {
        if (!caseUserService.checkNickNameUnique(caseUser))
        {
            return error("修改案例用户'" + caseUser.getNickName() + "'失败，昵称已存在");
        }
        else if (StringUtils.isNotEmpty(caseUser.getPhone()) && !caseUserService.checkPhoneUnique(caseUser))
        {
            return error("修改案例用户'" + caseUser.getNickName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(caseUser.getEmail()) && !caseUserService.checkEmailUnique(caseUser))
        {
            return error("修改案例用户'" + caseUser.getNickName() + "'失败，邮箱账号已存在");
        }
        caseUser.setUpdateBy(getUsername());
        return toAjax(caseUserService.updateCaseUser(caseUser));
    }

    /**
     * 删除案例用户
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:remove')")
    @Log(title = "案例用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(caseUserService.deleteCaseUserByUserIds(userIds));
    }

    /**
     * 导入案例用户数据
     */
    @PreAuthorize("@ss.hasPermi('system:caseUser:import')")
    @Log(title = "案例用户", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<CaseUser> util = new ExcelUtil<CaseUser>(CaseUser.class);
        List<CaseUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = caseUserService.importCaseUser(userList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载案例用户导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<CaseUser> util = new ExcelUtil<CaseUser>(CaseUser.class);
        util.importTemplateExcel(response, "案例用户数据");
    }

    // ========== 移动端API接口 ==========

    /**
     * 获取优秀客户列表（移动端）
     */
    @GetMapping("/mobile/excellent")
    public AjaxResult getExcellentUsers()
    {
        List<CaseUser> list = caseUserService.selectExcellentUsers(10);
        return success(list);
    }

    /**
     * 获取案例用户详细信息（移动端）
     */
    @GetMapping("/mobile/{userId}")
    public AjaxResult getMobileInfo(@PathVariable("userId") Long userId)
    {
        CaseUser caseUser = caseUserService.selectCaseUserByUserId(userId);
        if (caseUser == null)
        {
            return error("用户不存在");
        }
        return success(caseUser);
    }
}
