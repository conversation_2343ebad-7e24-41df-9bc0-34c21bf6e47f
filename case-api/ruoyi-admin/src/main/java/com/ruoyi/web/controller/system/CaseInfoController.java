package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.CaseInfo;
import com.ruoyi.system.service.ICaseInfoService;

/**
 * 案例信息Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/caseInfo")
public class CaseInfoController extends BaseController
{
    @Autowired
    private ICaseInfoService caseInfoService;

    /**
     * 查询案例信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaseInfo caseInfo)
    {
        startPage();
        List<CaseInfo> list = caseInfoService.selectCaseInfoList(caseInfo);
        return getDataTable(list);
    }

    /**
     * 导出案例信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:export')")
    @Log(title = "案例信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaseInfo caseInfo)
    {
        List<CaseInfo> list = caseInfoService.selectCaseInfoList(caseInfo);
        ExcelUtil<CaseInfo> util = new ExcelUtil<CaseInfo>(CaseInfo.class);
        util.exportExcel(response, list, "案例信息数据");
    }

    /**
     * 获取案例信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:query')")
    @GetMapping(value = "/{caseId}")
    public AjaxResult getInfo(@PathVariable("caseId") Long caseId)
    {
        return success(caseInfoService.selectCaseInfoByCaseId(caseId));
    }

    /**
     * 新增案例信息
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:add')")
    @Log(title = "案例信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CaseInfo caseInfo)
    {
        if (!caseInfoService.checkCaseTitleUnique(caseInfo))
        {
            return error("新增案例'" + caseInfo.getCaseTitle() + "'失败，案例标题已存在");
        }
        caseInfo.setCreateBy(getUsername());
        return toAjax(caseInfoService.insertCaseInfo(caseInfo));
    }

    /**
     * 修改案例信息
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:edit')")
    @Log(title = "案例信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CaseInfo caseInfo)
    {
        if (!caseInfoService.checkCaseTitleUnique(caseInfo))
        {
            return error("修改案例'" + caseInfo.getCaseTitle() + "'失败，案例标题已存在");
        }
        caseInfo.setUpdateBy(getUsername());
        return toAjax(caseInfoService.updateCaseInfo(caseInfo));
    }

    /**
     * 删除案例信息
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:remove')")
    @Log(title = "案例信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{caseIds}")
    public AjaxResult remove(@PathVariable Long[] caseIds)
    {
        return toAjax(caseInfoService.deleteCaseInfoByCaseIds(caseIds));
    }

    /**
     * 导入案例信息数据
     */
    @PreAuthorize("@ss.hasPermi('system:caseInfo:import')")
    @Log(title = "案例信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<CaseInfo> util = new ExcelUtil<CaseInfo>(CaseInfo.class);
        List<CaseInfo> caseInfoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = caseInfoService.importCaseInfo(caseInfoList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载案例信息导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<CaseInfo> util = new ExcelUtil<CaseInfo>(CaseInfo.class);
        util.importTemplateExcel(response, "案例信息数据");
    }

    // ========== 移动端API接口 ==========

    /**
     * 获取推荐案例列表（移动端）
     */
    @GetMapping("/mobile/recommended")
    public AjaxResult getRecommendedCases(@RequestParam(defaultValue = "10") Integer limit)
    {
        List<CaseInfo> list = caseInfoService.selectRecommendedCases(limit);
        return success(list);
    }

    /**
     * 获取最新案例列表（移动端）
     */
    @GetMapping("/mobile/latest")
    public AjaxResult getLatestCases(@RequestParam(defaultValue = "10") Integer limit)
    {
        List<CaseInfo> list = caseInfoService.selectLatestCases(limit);
        return success(list);
    }

    /**
     * 根据发布人ID获取案例列表（移动端）
     */
    @GetMapping("/mobile/publisher/{publisherId}")
    public AjaxResult getCasesByPublisher(@PathVariable("publisherId") Long publisherId)
    {
        List<CaseInfo> list = caseInfoService.selectCaseInfoByPublisherId(publisherId);
        return success(list);
    }

    /**
     * 根据标签获取案例列表（移动端）
     */
    @GetMapping("/mobile/tag/{tag}")
    public AjaxResult getCasesByTag(@PathVariable("tag") String tag)
    {
        List<CaseInfo> list = caseInfoService.selectCaseInfoByTag(tag);
        return success(list);
    }

    /**
     * 获取案例详细信息（移动端）
     */
    @GetMapping("/mobile/{caseId}")
    public AjaxResult getMobileInfo(@PathVariable("caseId") Long caseId)
    {
        CaseInfo caseInfo = caseInfoService.selectCaseInfoByCaseId(caseId);
        if (caseInfo == null)
        {
            return error("案例不存在");
        }
        // 增加点击次数
        caseInfoService.incrementClickCount(caseId);
        return success(caseInfo);
    }

    /**
     * 增加案例点击次数（移动端）
     */
    @PostMapping("/mobile/click/{caseId}")
    public AjaxResult incrementClick(@PathVariable("caseId") Long caseId)
    {
        return toAjax(caseInfoService.incrementClickCount(caseId));
    }
}
